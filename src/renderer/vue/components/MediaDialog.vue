<template>
  <div
    v-if="show"
    class="media-dialog"
    @keydown.esc="close"
    @click="close"
    tabindex="0"
  >
    <div class="media-dialog-container" @click.stop>
      <div class="media-dialog-header">
        <h3>{{ title }}</h3>
        <button class="close-btn" @click="close">×</button>
      </div>
      <div class="media-dialog-content">
        <!-- RTSP播放器 -->
        <RtspPlayer
          v-if="type === 'rtsp'"
          ref="rtspPlayerRef"
          :stream-url="url"
          :auto-play="true"
          :audio="false"
          :show-controls="false"
          @error="handleStreamError"
          @play="handleStreamPlay"
          @stop="handleStreamStop"
        />

        <!-- 图片或视频预览 -->
        <template v-else>
          <video
            v-if="type === 'video'"
            ref="videoPlayer"
            controls
            autoplay
            :src="url"
            @error="handleMediaError"
          ></video>
          <img
            v-else-if="type === 'image'"
            :src="url"
            :alt="title"
            @error="handleMediaError"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onBeforeUnmount } from "vue";
import RtspPlayer from "./RtspPlayer.vue";
import alertService from "../plugins/alert";

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "媒体预览",
  },
  type: {
    type: String,
    default: "image",
    validator: (value) => ["image", "video", "rtsp"].includes(value),
  },
  url: {
    type: String,
    default: "",
  },
  deviceSn: {
    type: String,
    default: "",
  },
});

// 定义事件
const emit = defineEmits(["close", "error"]);

// 引用
const videoPlayer = ref(null);
const rtspPlayerRef = ref(null);

// 监听show属性变化
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      // 显示对话框时设置焦点
      nextTick(() => {
        const dialogContainer = document.querySelector(".media-dialog");
        if (dialogContainer) {
          dialogContainer.focus();
        }

        if (props.type === "video" && videoPlayer.value) {
          videoPlayer.value.focus();
        }
      });
    }
  }
);

// 关闭对话框
const close = () => {
  // 如果是视频，暂停播放
  if (props.type === "video" && videoPlayer.value) {
    videoPlayer.value.pause();
  }

  // 如果是RTSP流，停止播放
  if (props.type === "rtsp" && rtspPlayerRef.value) {
    rtspPlayerRef.value.stop();
  }

  // 触发关闭事件
  emit("close");
};

// 处理媒体加载错误
const handleMediaError = (event) => {
  console.error(`媒体加载失败:`, event);

  // 显示错误提示
  alertService.alert({
    title: "加载失败",
    message: `无法加载${props.type === "video" ? "视频" : "图片"}文件`,
    type: "error",
  });

  // 触发错误事件
  emit("error", event);

  // 关闭对话框
  close();
};

// 处理RTSP流错误
const handleStreamError = (error) => {
  console.error("RTSP流错误:", error);

  // 显示错误提示
  alertService.alert({
    title: "流媒体错误",
    message: `无法加载流媒体: ${error.message || "未知错误"}`,
    type: "error",
  });

  // 触发错误事件
  emit("error", error);
};

// 处理RTSP流开始播放
const handleStreamPlay = () => {
  console.log("RTSP流开始播放");
};

// 处理RTSP流停止播放
const handleStreamStop = () => {
  console.log("RTSP流停止播放");
};

// 组件挂载后
onMounted(() => {
  // 添加键盘事件监听
  window.addEventListener("keydown", handleKeyDown);
});

// 组件卸载前
onBeforeUnmount(() => {
  // 移除键盘事件监听
  window.removeEventListener("keydown", handleKeyDown);
});

// 处理键盘事件
const handleKeyDown = (event) => {
  if (props.show && event.key === "Escape") {
    close();
  }
};
</script>

<style scoped>
.media-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.media-dialog-container {
  width: 90%;
  max-width: 1600px;
  height: 90vh;
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.media-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-card-background-online);
}

.media-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--color-text-primary);
}

.media-dialog-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-background-dark);
}

.media-dialog-content video,
.media-dialog-content img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* RTSP播放器样式 */
.media-dialog-content :deep(.video-container) {
  width: 100%;
  height: 100%;
}

/* 深色主题适配 */
[data-theme="dark"] .media-dialog-container {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .media-dialog-header {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .media-dialog-content {
  background-color: var(--color-background-darkest);
}
</style>
