<template>
  <div class="page">
    <div class="device-status-bar">
      <!-- 左侧统计 -->
      <div class="device-count">
        <span class="label">设备在线/总数：</span>
        <span class="online">{{ deviceStats.online }}</span>
        <span class="slash">/</span>
        <span>{{ deviceStats.total }}</span>
      </div>
      <!-- 状态图标区域 -->
      <div class="status-groups">
        <template v-for="(group, groupIndex) in statusGroups" :key="groupIndex">
          <div class="status-group">
            <div
              v-for="(item, itemIndex) in group"
              :key="itemIndex"
              class="status-item"
            >
              <img
                :src="getIconUrl(item.name)"
                :alt="item.label"
                class="status-icon"
              />
              <span class="status-label">{{ item.label }}</span>
            </div>
          </div>

          <div
            v-if="groupIndex < statusGroups.length - 1"
            class="divider"
          ></div>
        </template>
      </div>
    </div>

    <div class="header">
      <!-- 批量操作工具栏 -->
      <div
        v-if="isBatchMode"
        class="batch-toolbar"
        :class="{ hidden: !isBatchMode }"
      >
        <div class="batch-selection">
          <el-checkbox
            v-model="isAllSelected"
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
            size="large"
            >已选({{ selectedDevices.length }})</el-checkbox
          >
        </div>
        <div class="batch-actions">
          <div
            class="icon-btn"
            :class="{ disabled: batchBtnDisabled }"
            :disabled="batchBtnDisabled"
            @click="batchResetView"
          >
            <img
              src="@assets/svg/batch/view.svg"
              style="width: 16px; height: 16px"
              alt="reset"
            />
            <div>视野调整</div>
          </div>

          <div
            class="icon-btn"
            :disabled="batchBtnDisabled"
            @click="batchMonitor"
          >
            <img src="@assets/svg/batch/preview.svg" />
            <div>监控</div>
          </div>
          <div
            class="icon-btn"
            :disabled="batchBtnDisabled"
            @click="batchStopMonitor"
          >
            <img src="@assets/svg/batch/instrument.svg" />
            <div>取消监控</div>
          </div>

          <div
            class="icon-btn"
            :class="{ disabled: batchBtnDisabled }"
            :disabled="batchBtnDisabled"
            ref="volumeItemRef"
            @click="toggleVolumeControl"
          >
            <img src="@assets/svg/batch/sound.svg" alt="sound" />
            <div>音量控制</div>
          </div>
          <div
            class="icon-btn"
            :class="{ disabled: batchBtnDisabled }"
            :disabled="batchBtnDisabled"
            @click="batchShutdown"
          >
            <img src="@assets/svg/batch/off.svg" alt="off" />
            <div>关机</div>
          </div>

          <div
            class="icon-btn"
            :disabled="batchBtnDisabled"
            @click="batchRestart"
          >
            <img src="@assets/svg/batch/restart.svg" />
            <div>重启</div>
          </div>

          <div
            class="icon-btn more-btn"
            :disabled="batchBtnDisabled"
            @click="toggleBatchMoreMenu"
            ref="batchMoreMenuRef"
          >
            <img src="@assets/svg/card/more.svg" />
            <div>更多</div>
          </div>
          <!-- <button class="btn btn-secondary" @click="showAssignToGroupDialog">
          分配到组
        </button> -->
        </div>
      </div>

      <div v-else class="header-left">
        <el-select
          v-model="selectedGroupId"
          class="custom-select"
          @change="onGroupFilterChange"
          placeholder="默认分组"
        >
          <!-- 默认分组选项使用空字符串代替 null -->
          <el-option :value="''" label="默认分组"></el-option>

          <el-option
            v-for="group in deviceGroups"
            :key="group.id"
            :value="group.id"
            :label="group.name + ' (' + group.devices.length + ')'"
          />
        </el-select>
      </div>

      <div class="header-actions">
        <div class="text-btn" @click="showAddDeviceDialog">
          <img src="@assets/svg/card/btn_add.svg" alt="btn add" />
          添加设备
        </div>
        <div
          v-if="devices.length > 0"
          class="text-btn"
          :class="{ active: isBatchMode }"
          @click="toggleBatchMode"
        >
          <img src="@assets/svg/card/btn_manager.svg" alt="btn manager" />
          批量管理
        </div>
        <!-- <div class="text-btn" @click="showGroupManager">
          <img src="@assets/svg/card/btn_add.svg" alt="btn add" />分组管理
        </div> -->
      </div>
    </div>

    <div class="content-layout">
      <!-- 设备列表 -->
      <div class="device-content">
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>正在加载设备列表...</p>
        </div>

        <div v-else-if="error" class="error">
          <p>{{ error }}</p>
          <button class="btn btn-primary" @click="refreshDevices">重试</button>
        </div>

        <div v-else-if="devices.length === 0" class="empty-list">
          <img
            src="@assets/svg/add_img_btn.svg"
            alt="add-btn"
            @click="showAddDeviceDialog"
          />
          <span>请先添加设备</span>
          <pre class="add-tips">
添加设备前，请完成以下3步操作：
1.前往VR设备【设置】-【网络】，将VR设备与电脑
连接在同一局域网下
2.在【资源库】打开【企业应用套件】应用 
3.在播控应用主界面，点击设置。打开"接受控制端
控制开关。 
完成以上3步。
          </pre>
        </div>

        <div v-else class="device-list">
          <div
            class="device-grid"
            :class="{ 'list-view': viewMode === 'list' }"
          >
            <DeviceCard
              v-for="device in filteredDevices"
              :key="device.id || device.sn"
              :device="device"
              :batch-mode="isBatchMode"
              :selected="selectedDevices.includes(device.sn)"
              :monitored-devices="monitoredDevices"
              @edit="editDevice"
              @delete="deleteDevice"
              @volume="controlVolume"
              @screen-off="screenOff"
              @shutdown="shutdownDevice"
              @restart="restartDevice"
              @select="handleDeviceSelect"
              @deselect="handleDeviceDeselect"
              @update="handleDeviceUpdate"
              @locate="locateDevice"
              @cast="openCastDialog"
              @reset-view="resetDeviceView"
              @maximize-stream="openMediaDialog"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 分组管理对话框 -->
    <DeviceGroupDialog
      v-if="showGroupDialog"
      :groups="deviceGroups"
      :devices="devices"
      @close="closeGroupDialog"
      @save="saveDeviceGroup"
      @delete="deleteDeviceGroup"
      @addDeviceToGroup="handleAddDeviceToGroup"
      @removeDeviceFromGroup="handleRemoveDeviceFromGroup"
    />

    <!-- 设备分配对话框 -->
    <DeviceGroupAssignment
      v-if="showAssignmentDialog"
      :devices="devices"
      :groups="deviceGroups"
      :initial-selected-devices="selectedDevices"
      @cancel="showAssignmentDialog = false"
      @assign="assignDevicesToGroup"
      @createGroup="handleCreateGroupFromAssignment"
    />

    <!-- 设备编辑对话框 -->
    <DeviceEditDialog
      v-if="showEditDialog"
      :device="currentEditDevice"
      @save="handleDeviceUpdate"
      @cancel="showEditDialog = false"
    />

    <!-- 媒体对话框 - 用于放大显示流媒体 -->
    <MediaDialog
      v-if="showMediaDialog"
      :show="showMediaDialog"
      :title="mediaDialogTitle"
      :type="mediaDialogType"
      :url="mediaDialogUrl"
      :device-sn="mediaDialogDeviceSn"
      @close="closeMediaDialog"
    />

    <!-- 批量更多菜单 -->
    <teleport to="body">
      <div
        v-if="showBatchMoreMenu"
        class="batch-more-menu"
        ref="batchMoreMenuRef"
        :style="{
          top: batchMoreMenuPosition.top,
          left: batchMoreMenuPosition.left,
        }"
        v-click-outside="
          (event) => {
            // 检查点击是否来自更多按钮
            if (event.target.closest('.more-btn')) {
              return;
            }
            console.log('点击批量更多菜单外部');
            showBatchMoreMenu = false;
          }
        "
      >
        <!-- <div
          class="dropdown-item"
          ref="volumeItemRef"
          @click="toggleVolumeControl"
        >
          音量控制
        </div> -->
        <div class="dropdown-item" @click="batchScreenOff">关闭屏幕</div>
        <div class="dropdown-item" @click="batchScreenOn">开启屏幕</div>
        <div class="dropdown-item" @click="batchHideShortcut">隐藏快捷入口</div>
        <div class="dropdown-item" @click="batchShowShortcut">显示快捷入口</div>
        <div class="dropdown-item" @click="batchDelete">删除</div>
      </div>
    </teleport>

    <!-- 音量控制子菜单 -->
    <teleport to="body" v-if="showVolumeSlider">
      <div
        class="volume-submenu"
        :style="{ top: volumePosition.top, left: volumePosition.left }"
        @mouseenter="volumeHovered = true"
        @mouseleave="volumeHovered = false"
        @click.stop
        v-click-outside="
          () => {
            console.log('点击外部事件触发');
            if (!isAdjustingVolume && !justAdjustedVolume) {
              console.log('隐藏音量控制条');
              showVolumeSlider = false;
            }
          }
        "
      >
        <div class="volume-slider-container">
          <div class="volume-value">
            <span v-if="isLoadingVolume" class="loading-indicator-small"></span>
            <span v-else>{{ volumeValue }}</span>
          </div>
          <input
            type="range"
            class="volume-slider"
            min="0"
            max="100"
            :value="volumeValue"
            @input="updateVolumeDisplay"
            @mousedown="startAdjustingVolume"
            @mouseup="stopAdjustingVolume"
            @change="stopAdjustingVolume"
            :disabled="isLoadingVolume"
          />
          <i class="icon-volume-img icon-invert" style="margin-top: 8px"></i>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  getCurrentInstance,
  watch,
  nextTick,
} from "vue";
import { useRouter } from "vue-router";
import alertService from "../plugins/alert";
import { useElectronAPI } from "../plugins/electron";
import DeviceCard from "../components/DeviceCard.vue";
import DeviceGroupDialog from "../components/DeviceGroupDialog.vue";
import DeviceGroupAssignment from "../components/DeviceGroupAssignment.vue";
import CustomSelect from "../components/CustomSelect.vue";
import DeviceEditDialog from "../components/DeviceEditDialog.vue";
import MediaDialog from "../components/MediaDialog.vue";
import { DeviceEvents } from "../../../shared/constants/events";
import { stopAllScreenCasts } from "../services/stream-service";
import { CommandType } from "../../../shared/constants/command-types";
import { ElMessage } from "element-plus";
const statusGroups = [
  [
    { name: "online", label: "在线" },
    { name: "online_no", label: "离线" },
  ],
  [
    { name: "use_no", label: "未佩戴" },
    { name: "use", label: "已佩戴" },
  ],
  [
    { name: "controlled_no", label: "不受控" },
    { name: "controlled", label: "受控中" },
  ],
  [
    { name: "play", label: "播放" },
    { name: "play_pause", label: "暂停" },
    { name: "play_error", label: "播放异常" },
  ],
];
const getIconUrl = (name) =>
  new URL(`../../assets/svg/status/${name}.svg`, import.meta.url).href;

// 使用路由
const router = useRouter();

// 使用Electron API
const electronAPI = useElectronAPI();

// 状态
const devices = ref([]);
const devicesMap = ref(new Map()); // 使用Map存储设备，与原DeviceModel保持一致
const loading = ref(false);
const error = ref(null);
const searchQuery = ref("");
const statusFilter = ref("all");
const viewMode = ref("grid");

// 批量管理状态
const isBatchMode = ref(false);
const selectedDevices = ref([]);

// 设备组状态
const deviceGroups = ref([]);
const selectedGroupId = ref("");
const showGroupDialog = ref(false);
const showAssignmentDialog = ref(false);
const groupDevices = ref([]);

// 设备编辑状态
const showEditDialog = ref(false);
const currentEditDevice = ref(null);

// 媒体对话框状态
const showMediaDialog = ref(false);
const mediaDialogTitle = ref("");
const mediaDialogType = ref("");
const mediaDialogUrl = ref("");
const mediaDialogDeviceSn = ref("");

// 批量更多菜单状态
const showBatchMoreMenu = ref(false);
const batchMoreMenuRef = ref(null);
const batchMoreMenuPosition = ref({ top: 0, left: 0 });

// 音量控制相关状态
const showVolumeSlider = ref(false);
const volumeValue = ref(50);
const volumeItemRef = ref(null);
const volumePosition = ref({ top: 0, left: 0 });
const volumeHovered = ref(false);
const isAdjustingVolume = ref(false);
const justAdjustedVolume = ref(false);
const isLoadingVolume = ref(false);

// 切换音量控制显示状态
const toggleVolumeControl = async (event) => {
  if (batchBtnDisabled.value) {
    return;
  }
  event.stopPropagation();
  console.log("切换音量控制显示状态:", !showVolumeSlider.value);

  // 切换显示状态
  showVolumeSlider.value = !showVolumeSlider.value;

  // 如果显示，则更新位置
  if (showVolumeSlider.value) {
    // 等待DOM更新
    await nextTick();

    // 获取菜单项元素
    const volumeItem = event.currentTarget;
    if (!volumeItem) {
      console.error("无法获取音量控制菜单项");
      return;
    }

    // 计算音量控制条的位置，使其显示在菜单项右侧
    const itemRect = volumeItem.getBoundingClientRect();
    volumePosition.value = {
      top: itemRect.top - 60 + "px", // 垂直居中
      left: itemRect.right + 10 + "px", // 在菜单项右侧显示
    };
    console.log("音量控制条位置:", volumePosition.value);
  } else {
    console.log("隐藏音量控制条");
  }
};

// 开始调整音量
const startAdjustingVolume = (event) => {
  event.stopPropagation();
  isAdjustingVolume.value = true;
  console.log("开始调整音量, isAdjustingVolume:", isAdjustingVolume.value);
};

// 停止调整音量
const stopAdjustingVolume = (event) => {
  event.stopPropagation();
  isAdjustingVolume.value = false;
  justAdjustedVolume.value = true;
  console.log(
    "停止调整音量, isAdjustingVolume:",
    isAdjustingVolume.value,
    "justAdjustedVolume:",
    justAdjustedVolume.value
  );

  // 直接发送音量调节命令
  updateVolume();

  // 设置一个定时器，在短暂延迟后重置标志
  setTimeout(() => {
    justAdjustedVolume.value = false;
    console.log("重置justAdjustedVolume标志");
  }, 100);
};

// 更新音量显示值（滑动过程中）
const updateVolumeDisplay = (event) => {
  event.stopPropagation();
  volumeValue.value = parseInt(event.target.value);
};

// 更新音量（发送指令）
const updateVolume = async () => {
  try {
    // 检查设备在线状态
    const onlineDevices = [];
    for (const sn of selectedDevices.value) {
      const isOnline = await electronAPI.isDeviceOnline(sn);
      if (isOnline) {
        onlineDevices.push(sn);
      }
    }

    if (onlineDevices.length === 0) {
      ElMessage.warning("没有在线设备可操作");
      return;
    }

    await electronAPI.emit("device:batch-volume", {
      deviceList: onlineDevices,
      volume: volumeValue.value,
    });
  } catch (error) {
    console.error("批量控制音量失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量控制音量失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 监听设备在线状态，当设备离线时自动关闭 MediaDialog
watch(
  () => devices.value,
  (newDevices) => {
    if (showMediaDialog.value && mediaDialogDeviceSn.value) {
      // 查找当前显示的设备
      const device = newDevices.find((d) => d.sn === mediaDialogDeviceSn.value);
      // 如果设备不存在或离线，关闭对话框
      if (!device || !device.isOnline) {
        console.log(
          `设备 ${mediaDialogDeviceSn.value} 离线或不存在，关闭媒体对话框`
        );
        closeMediaDialog();
      }
    }
  },
  { deep: true }
);

// 当前选中的组
const selectedGroup = computed(() => {
  if (!selectedGroupId.value) return null;
  return (
    deviceGroups.value.find((group) => group.id === selectedGroupId.value) ||
    null
  );
});

// 计算属性 - 是否全选
const isAllSelected = computed(() => {
  return (
    filteredDevices.value.length > 0 &&
    selectedDevices.value.length === filteredDevices.value.length
  );
});

// 计算属性 - 是否全选
const batchBtnDisabled = computed(() => {
  return selectedDevices.value.length == 0;
});

// 计算属性 - 是否部分选中
const isIndeterminate = computed(() => {
  return (
    selectedDevices.value.length > 0 &&
    selectedDevices.value.length < filteredDevices.value.length
  );
});

// 计算属性
const filteredDevices = computed(() => {
  const filtered = devices.value.filter((device) => {
    // 组过滤
    if (selectedGroupId.value) {
      const group = deviceGroups.value.find(
        (g) => g.id === selectedGroupId.value
      );
      if (group && !group.devices.includes(device.sn)) {
        return false;
      }
    }

    // 状态过滤
    if (statusFilter.value !== "all") {
      if (statusFilter.value === "online" && !device.isOnline) {
        return false;
      }
      if (statusFilter.value === "offline" && device.isOnline) {
        return false;
      }
    }

    // 搜索过滤
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      return (
        (device.sn || "").toLowerCase().includes(query) ||
        (device.ip || "").toLowerCase().includes(query) ||
        (String(device.id) || "").toLowerCase().includes(query)
      );
    }

    return true;
  });

  // 按设备 ID 排序
  return filtered.sort((a, b) => {
    const idA = a.id || 0;
    const idB = b.id || 0;
    return idA - idB;
  });
});

// 添加计算属性
const deviceStats = computed(() => {
  const total = devices.value.length;
  const online = devices.value.filter((device) => device.isOnline).length;
  return { total, online };
});

// 在 script setup 部分添加监控设备记录
const monitoredDevices = ref(new Set()); // 记录正在监控的设备

// 方法
const fetchDevices = async () => {
  try {
    loading.value = true;
    error.value = null;

    // 获取设备历史记录
    const historyDevices = await electronAPI.getDeviceHistory();
    console.log("DevicesPage: 从API获取的设备历史:", historyDevices);

    // 清空设备Map
    devicesMap.value.clear();

    if (!historyDevices || historyDevices.length === 0) {
      console.log("DevicesPage: 没有历史设备");
      devices.value = [];
      loading.value = false;
      return;
    }

    // 更新设备列表
    for (const device of historyDevices) {
      const deviceData = {
        sn: device.sn,
        id: device.id,
        isOnline: device.isOnline,
        deviceStatus: device.isOnline ? 1 : 0,
        battery: device.status?.battery || "-",
        onuse: device.status?.onuse || false,
        playStatus: device.isOnline ? device.status?.playStatus || "-" : "-", // 离线设备的播放状态设置为'-'
        controlled: device.status?.controlled || false,
        addedAt: device.addedAt || Date.now(),
      };

      devicesMap.value.set(device.sn, deviceData);
    }
    mapToListAndRank();
    console.log(
      "DevicesPage: 设备列表加载完成，共",
      devices.value.length,
      "个设备"
    );
  } catch (err) {
    error.value = err.message || "获取设备列表失败";
    console.error("获取设备列表失败:", err);
  } finally {
    loading.value = false;
  }
};

// 将Map转换为数组并排序
const mapToListAndRank = () => {
  devices.value = Array.from(devicesMap.value.values());
  sortingCardList();
};

const refreshDevices = () => {
  fetchDevices();
};

const showAddDeviceDialog = () => {
  // 使用路由导航到添加设备页面
  router.push("/add-device");
};

// 批量管理相关方法
const toggleBatchMode = () => {
  isBatchMode.value = !isBatchMode.value;

  // 退出批量模式时清空选择
  if (!isBatchMode.value) {
    selectedDevices.value = [];
  }
};

// 切换全选状态
const handleCheckAllChange = (val) => {
  selectedDevices.value = val
    ? filteredDevices.value.map((device) => device.sn)
    : [];
  isIndeterminate.value = false;
};

// 处理设备选择
const handleDeviceSelect = (sn) => {
  if (!selectedDevices.value.includes(sn)) {
    selectedDevices.value.push(sn);
  }
};

// 处理设备取消选择
const handleDeviceDeselect = (sn) => {
  selectedDevices.value = selectedDevices.value.filter(
    (deviceSn) => deviceSn !== sn
  );
};

// 批量重启设备
const batchRestart = async () => {
  if (selectedDevices.value.length === 0) return;

  const confirmed = await alertService.confirm({
    title: "批量重启设备",
    message: `确定要重启选中的 ${selectedDevices.value.length} 个设备吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      let successCount = 0;
      let failCount = 0;
      let offlineCount = 0;

      for (const sn of selectedDevices.value) {
        try {
          // 检查设备是否在线
          const isOnline = await electronAPI.isDeviceOnline(sn);
          if (!isOnline) {
            offlineCount++;
            continue;
          }

          // 发送重启事件
          electronAPI.emit("device:restart", sn);
          console.log(`设备 ${sn} 重启命令已发送`);

          // 手动更新设备状态为离线
          const deviceInMap = devicesMap.value.get(sn);
          if (deviceInMap) {
            const updatedDevice = {
              ...deviceInMap,
              isOnline: false,
              deviceStatus: 0,
              battery: "-",
              onuse: false,
              playStatus: "-",
              controlled: false,
            };

            // 更新设备Map
            devicesMap.value.set(sn, updatedDevice);
          }

          successCount++;
        } catch (error) {
          console.error(`重启设备 ${sn} 失败:`, error);
          failCount++;
        }
      }

      // 更新设备数组
      mapToListAndRank();

      // 显示结果
      let message = "";
      if (successCount > 0) {
        message += `成功发送 ${successCount} 个设备的重启命令。`;
      }
      if (failCount > 0) {
        message += `${failCount} 个设备重启失败。`;
      }
      if (offlineCount > 0) {
        message += `${offlineCount} 个设备离线。`;
      }

      ElMessage.success(message);

      // 清空选择
      selectedDevices.value = [];
    } catch (error) {
      console.error("批量重启设备失败:", error);
      await alertService.alert({
        title: "操作失败",
        message: `批量重启设备失败: ${error.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

// 批量关机
const batchShutdown = async () => {
  if (selectedDevices.value.length === 0) return;

  const confirmed = await alertService.confirm({
    title: "批量关机",
    message: `确定要关闭选中的 ${selectedDevices.value.length} 个设备吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      let successCount = 0;
      let failCount = 0;
      let offlineCount = 0;

      for (const sn of selectedDevices.value) {
        try {
          // 检查设备是否在线
          const isOnline = await electronAPI.isDeviceOnline(sn);
          if (!isOnline) {
            offlineCount++;
            continue;
          }

          // 发送关机事件
          electronAPI.emit("device:shutdown", sn);
          console.log(`设备 ${sn} 关机命令已发送`);

          // 手动更新设备状态为离线
          const deviceInMap = devicesMap.value.get(sn);
          if (deviceInMap) {
            const updatedDevice = {
              ...deviceInMap,
              isOnline: false,
              deviceStatus: 0,
              battery: "-",
              onuse: false,
              playStatus: "-",
              controlled: false,
            };

            // 更新设备Map
            devicesMap.value.set(sn, updatedDevice);
          }

          successCount++;
        } catch (error) {
          console.error(`关闭设备 ${sn} 失败:`, error);
          failCount++;
        }
      }

      // 更新设备数组
      mapToListAndRank();

      // 显示结果
      let message = "";
      if (successCount > 0) {
        message += `成功发送 ${successCount} 个设备的关机命令。`;
      }
      if (failCount > 0) {
        message += `${failCount} 个设备关机失败。`;
      }
      if (offlineCount > 0) {
        message += `${offlineCount} 个设备离线。`;
      }

      ElMessage.success(message);

      // 清空选择
      selectedDevices.value = [];
    } catch (error) {
      console.error("批量关机失败:", error);
      await alertService.alert({
        title: "操作失败",
        message: `批量关机失败: ${error.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

// 批量删除
const batchDelete = async () => {
  if (selectedDevices.value.length === 0) return;

  const confirmed = await alertService.confirm({
    title: "批量删除设备",
    message: `确定要删除选中的 ${selectedDevices.value.length} 个设备吗？此操作不可恢复。`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      let successCount = 0;
      let failCount = 0;

      for (const sn of selectedDevices.value) {
        try {
          // 调用IPC方法删除设备
          await electronAPI.deleteDevice(sn);

          // 从Map中删除设备
          devicesMap.value.delete(sn);

          successCount++;
        } catch (error) {
          console.error(`删除设备 ${sn} 失败:`, error);
          failCount++;
        }
      }

      // 更新设备数组
      mapToListAndRank();

      // 刷新设备组列表，确保分组过滤器更新
      await fetchDeviceGroups();

      // 如果当前有选中的组，重新加载组内设备
      if (selectedGroupId.value) {
        loadGroupDevices(selectedGroupId.value);
      }

      // 显示结果
      let message = "";
      if (successCount > 0) {
        message += `成功删除 ${successCount} 个设备。`;
        ElMessage.success(message);
      }
      if (failCount > 0) {
        message += `${failCount} 个设备删除失败。`;
        ElMessage.warning(message);
      }

      // 清空选择
      selectedDevices.value = [];

      // 如果删除后没有设备了，自动退出批量管理模式
      if (devices.value.length === 0) {
        isBatchMode.value = false;
        console.log("所有设备已删除，自动退出批量管理模式");
      }
    } catch (error) {
      console.error("批量删除设备失败:", error);
      await alertService.alert({
        title: "操作失败",
        message: `批量删除设备失败: ${error.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

const editDevice = (device) => {
  // 设置当前编辑的设备
  currentEditDevice.value = device;
  // 显示编辑对话框
  showEditDialog.value = true;
  console.log("编辑设备:", device);
};

// 处理设备更新
const handleDeviceUpdate = async (updatedData) => {
  //console.log('处理设备更新:', updatedData);

  try {
    // 关闭编辑对话框
    showEditDialog.value = false;

    // 1. 更新设备ID
    if (updatedData.id !== undefined) {
      // 调用API更新设备ID，传递forceSwap参数
      const result = await electronAPI.updateDeviceId(
        updatedData.sn,
        updatedData.id,
        updatedData.forceSwap || false
      );

      // 更新本地设备数据
      const deviceInMap = devicesMap.value.get(updatedData.sn);
      if (deviceInMap) {
        deviceInMap.id = updatedData.id;
        devicesMap.value.set(updatedData.sn, deviceInMap);
      }

      // 如果进行了ID交换，还需要更新另一个设备的ID
      if (result.swappedWith) {
        const swappedDevice = devicesMap.value.get(result.swappedWith.sn);
        if (swappedDevice) {
          swappedDevice.id = result.swappedWith.id;
          devicesMap.value.set(result.swappedWith.sn, swappedDevice);
        }
      }
    }

    // 2. 处理分组更改
    if (updatedData.addedGroups && updatedData.addedGroups.length > 0) {
      for (const groupId of updatedData.addedGroups) {
        await electronAPI.addDeviceToGroup(groupId, updatedData.sn);
      }
    }

    if (updatedData.removedGroups && updatedData.removedGroups.length > 0) {
      for (const groupId of updatedData.removedGroups) {
        await electronAPI.removeDeviceFromGroup(groupId, updatedData.sn);
      }
    }

    // 3. 刷新设备列表和分组
    await fetchDevices();
    await fetchDeviceGroups();

    // 显示成功提示
    let successMessage = "设备信息已成功更新";

    // 如果进行了ID交换，显示更详细的成功信息
    if (updatedData.forceSwap) {
      successMessage = `设备信息已成功更新，ID已成功交换`;
    }
    ElMessage.success(successMessage);
  } catch (error) {
    console.error("更新设备信息失败:", error);
    ElMessage.error("更新设备信息失败");
  }
};
const sortingCardList = () => {
  // 按id升序排序设备
  devices.value = devices.value.slice().sort((a, b) => {
    if (a.id != null && b.id != null) {
      return Number(a.id) - Number(b.id);
    }
    return String(a.id).localeCompare(String(b.id));
  });
};

const deleteDevice = async (device) => {
  // 使用自定义确认对话框
  const confirmed = await alertService.confirm({
    title: "删除设备",
    message: `确定要删除设备 "${device.sn}" 吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      // 调用IPC方法删除设备
      await electronAPI.deleteDevice(device.sn);

      // 从Map中删除设备
      devicesMap.value.delete(device.sn);

      // 更新设备数组
      mapToListAndRank();

      // 刷新设备组列表，确保分组过滤器更新
      await fetchDeviceGroups();

      // 如果当前有选中的组，重新加载组内设备
      if (selectedGroupId.value) {
        loadGroupDevices(selectedGroupId.value);
      }

      ElMessage.success(`设备 ${device.sn} 已成功删除`);
    } catch (err) {
      error.value = err.message || "删除设备失败";
      console.error("删除设备失败:", err);

      // 显示错误提示
      await alertService.alert({
        title: "操作失败",
        message: `删除设备失败: ${err.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

const controlVolume = (device) => {
  // TODO: 实现音量控制功能
  console.log("控制设备音量:", device);
  electronAPI.emit("device:controlVolume", device);
};

const screenOff = (device) => {
  // 实现关闭屏幕功能
  console.log("关闭设备屏幕:", device);

  // 检查设备是否在线
  if (!device.isOnline) {
    alertService.alert({
      title: "设备离线",
      message: `设备 ${device.sn} 不在线，无法关闭屏幕`,
      confirmButtonText: "确定",
    });
    return;
  }

  // 发送关闭屏幕命令
  electronAPI.emit("device:screenOff", device);
};

// 重置设备视野 - 功能留空
const resetDeviceView = (device) => {
  console.log("重置设备视野功能留空:", device);
  // 功能留空，仅记录日志
};

// 打开媒体对话框 - 用于放大显示流媒体
const openMediaDialog = (data) => {
  console.log("打开媒体对话框:", data);

  // 设置媒体对话框状态
  mediaDialogTitle.value = `设备 ${data.device.id || data.device.sn} 的投屏`;
  mediaDialogType.value = "rtsp";
  mediaDialogUrl.value = data.streamUrl;
  mediaDialogDeviceSn.value = data.device.sn;

  // 显示媒体对话框
  showMediaDialog.value = true;
};

// 关闭媒体对话框
const closeMediaDialog = () => {
  console.log("关闭媒体对话框");
  showMediaDialog.value = false;
  mediaDialogTitle.value = "";
  mediaDialogType.value = "";
  mediaDialogUrl.value = "";
  mediaDialogDeviceSn.value = "";
};

// 定位设备 - 发送index=-1的播控命令
const locateDevice = async (device) => {
  console.log("定位设备:", device);

  // 检查设备是否在线
  if (!device.isOnline) {
    await alertService.alert({
      title: "设备离线",
      message: `设备 ${device.sn} 不在线，无法定位`,
      confirmButtonText: "确定",
    });
    return;
  }

  try {
    // 使用播控命令API，发送index=-1的命令
    await electronAPI.publishResource(-1, [device.sn], false);
    console.log(`设备 ${device.sn} 定位命令已发送`);
  } catch (err) {
    console.error("定位设备失败:", err);
    await alertService.alert({
      title: "操作失败",
      message: `定位设备失败: ${err.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 打开投屏对话框 - 功能留空
const openCastDialog = (device) => {
  console.log("投屏功能留空:", device);
  // 功能留空，仅记录日志
};

const shutdownDevice = async (device) => {
  // 使用自定义确认对话框
  const confirmed = await alertService.confirm({
    title: "关闭设备",
    message: `确定要关闭设备 "${device.sn}" 吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      // 检查设备是否在线
      const isOnline = await electronAPI.isDeviceOnline(device.sn);
      if (!isOnline) {
        // 使用自定义提示对话框
        await alertService.alert({
          title: "设备离线",
          message: `设备 ${device.sn} 不在线，无法关机`,
          confirmButtonText: "确定",
        });
        return;
      }

      // 发送关机事件
      electronAPI.emit("device:shutdown", device.sn);
      console.log(`设备 ${device.sn} 关机命令已发送`);

      // 手动更新设备状态为离线（关机后设备会离线）
      // 这样即使设备断开连接事件没有立即触发，UI也会立即更新
      setTimeout(() => {
        const deviceInMap = devicesMap.value.get(device.sn);
        if (deviceInMap) {
          const updatedDevice = {
            ...deviceInMap,
            isOnline: false,
            deviceStatus: 0,
            battery: "-",
            onuse: false,
            playStatus: "-",
            controlled: false,
          };

          // 更新设备Map
          devicesMap.value.set(device.sn, updatedDevice);
          // 更新设备数组
          mapToListAndRank();

          console.log(`设备 ${device.sn} 状态已手动更新为离线`);
        }
      }, 1000); // 延迟1秒更新，给设备一些响应时间
    } catch (err) {
      console.error("关闭设备失败:", err);
      // 使用自定义提示对话框
      await alertService.alert({
        title: "操作失败",
        message: `关闭设备失败: ${err.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

const restartDevice = async (device) => {
  // 使用自定义确认对话框
  const confirmed = await alertService.confirm({
    title: "重启设备",
    message: `确定要重启设备 "${device.sn}" 吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      // 检查设备是否在线
      const isOnline = await electronAPI.isDeviceOnline(device.sn);
      if (!isOnline) {
        // 使用自定义提示对话框
        await alertService.alert({
          title: "设备离线",
          message: `设备 ${device.sn} 不在线，无法重启`,
          confirmButtonText: "确定",
        });
        return;
      }

      // 发送重启事件
      electronAPI.emit("device:restart", device.sn);
      console.log(`设备 ${device.sn} 重启命令已发送`);

      // 手动更新设备状态为离线（重启过程中设备会暂时离线）
      // 这样即使设备断开连接事件没有立即触发，UI也会立即更新
      setTimeout(() => {
        const deviceInMap = devicesMap.value.get(device.sn);
        if (deviceInMap) {
          const updatedDevice = {
            ...deviceInMap,
            isOnline: false,
            deviceStatus: 0,
            battery: "-",
            onuse: false,
            playStatus: "-",
            controlled: false,
          };

          // 更新设备Map
          devicesMap.value.set(device.sn, updatedDevice);
          // 更新设备数组
          mapToListAndRank();

          console.log(`设备 ${device.sn} 状态已手动更新为离线（重启中）`);
        }
      }, 1000); // 延迟1秒更新，给设备一些响应时间
    } catch (err) {
      console.error("重启设备失败:", err);
      // 使用自定义提示对话框
      await alertService.alert({
        title: "操作失败",
        message: `重启设备失败: ${err.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

// 处理设备连接事件
const handleDeviceConnected = async ({ ip, status }) => {
  if (!status?.sn) return;

  try {
    // 检查设备是否已添加
    const isDeviceAdded = await electronAPI.isDeviceAdded(status.sn);
    if (!isDeviceAdded) return; // 如果设备未添加，不显示在列表中

    const deviceData = {
      sn: status.sn,
      id: status.deviceId,
      isOnline: true,
      deviceStatus: 1,
      battery: status.battery || "-",
      onuse: status.onuse || false,
      playStatus: status.playStatus || "-",
      controlled: status.controlled || false,
      ip: ip,
      addedAt: Date.now(),
    };

    console.log("DevicesPage: 设备连接，创建设备数据:", deviceData);

    // 更新设备Map
    devicesMap.value.set(status.sn, deviceData);
    // 更新设备数组
    mapToListAndRank();
  } catch (error) {
    console.error("DevicesPage: 处理设备连接事件失败:", error);
  }
};

// 处理设备断开连接事件
const handleDeviceDisconnected = async (ip) => {
  try {
    console.log("DevicesPage: 设备断开连接:", ip);

    // 查找对应的设备并更新状态
    for (const [sn, device] of devicesMap.value.entries()) {
      if (device.ip === ip) {
        // 更新设备状态为离线
        const updatedDevice = {
          ...device,
          isOnline: false,
          deviceStatus: 0,
          battery: "-",
          onuse: false,
          playStatus: "-",
          controlled: false,
        };

        // 更新设备Map
        devicesMap.value.set(sn, updatedDevice);
      }
    }

    // 更新设备数组
    mapToListAndRank();
  } catch (error) {
    console.error("DevicesPage: 处理设备断开连接事件失败:", error);
  }
};

// 处理设备状态更新事件
const handleDeviceStatusUpdated = async ({ ip, status }) => {
  if (!status.sn) return;

  const device = devicesMap.value.get(status.sn);
  if (!device) return;

  const updatedDevice = {
    ...device,
    isOnline: status.deviceStatus === 1, // 根据deviceStatus判断在线状态
    deviceStatus: status.deviceStatus,
    battery: status.battery || device.battery,
    onuse: status.onuse || device.onuse,
    playStatus: status.playStatus || device.playStatus,
    controlled: status.controlled || device.controlled,
    ip: ip,
  };

  // 更新设备Map
  devicesMap.value.set(status.sn, updatedDevice);
  // 更新设备数组
  mapToListAndRank();
};

// 设备相关事件处理

// 生命周期钩子
onMounted(async () => {
  // 加载设备列表
  await fetchDevices();

  // 加载设备组列表
  await fetchDeviceGroups();

  // 设置事件监听器
  setupEventListeners();

  // 在onMounted中添加点击外部关闭菜单的监听
  document.addEventListener("click", handleClickOutside, true);
  console.log("组件挂载，添加点击事件监听器");
});

onUnmounted(() => {
  // 移除事件监听器
  removeEventListeners();

  // 停止所有投屏，不等待完成
  stopAllScreenCasts(false, 100); // 不等待完成，每个停止操作之间延迟100毫秒

  // 清空监控设备记录
  monitoredDevices.value.clear();

  // 在onUnmounted中移除监听
  document.removeEventListener("click", handleClickOutside, true);
  console.log("组件卸载，移除点击事件监听器");
});

// 设置事件监听器
const setupEventListeners = () => {
  // 监听设备连接事件
  electronAPI.onDeviceConnected(handleDeviceConnected);

  // 监听设备断开连接事件
  electronAPI.onDeviceDisconnected(handleDeviceDisconnected);

  // 监听设备状态更新事件
  electronAPI.onDeviceStatusUpdate(handleDeviceStatusUpdated);

  console.log("DevicesPage: 事件监听器设置完成");
};

// 移除事件监听器
const removeEventListeners = () => {
  // 移除设备连接事件监听器
  electronAPI.offDeviceConnected(handleDeviceConnected);

  // 移除设备断开连接事件监听器
  electronAPI.offDeviceDisconnected(handleDeviceDisconnected);

  // 移除设备状态更新事件监听器
  electronAPI.offDeviceStatusUpdate(handleDeviceStatusUpdated);
};

// 处理批量快捷入口操作响应
const handleBatchShortcutResponse = (result) => {
  console.log("批量快捷入口操作响应:", result);

  // 如果有失败的操作，显示错误提示
  if (result.failed && result.failed.length > 0) {
    const failedDevices = result.failed.map((f) => f.sn).join(", ");
    alertService.alert({
      title: "操作失败",
      message: `以下设备操作失败: ${failedDevices}`,
      confirmButtonText: "确定",
    });
  }
};

// 设备组相关方法
// 获取设备组列表
const fetchDeviceGroups = async () => {
  try {
    const groups = await electronAPI.getDeviceGroups();
    deviceGroups.value = groups.map((group) => ({
      ...group,
      id: String(group.id), // 确保所有 id 都是字符串
      devices: group.devices || [], // 确保 devices 数组存在
    }));
    console.log("DevicesPage: 设备组列表加载完成，共", groups.length, "个组");
  } catch (error) {
    console.error("获取设备组列表失败:", error);
  }
};

// 刷新设备组列表（可以在其他地方调用）
function refreshDeviceGroups() {
  fetchDeviceGroups();
}

// 处理分组过滤器变化
const onGroupFilterChange = () => {
  // 如果选择了组，加载组内设备
  if (selectedGroupId.value) {
    loadGroupDevices(selectedGroupId.value);
  } else {
    groupDevices.value = [];
  }

  console.log("DevicesPage: 分组过滤器变化，选中组ID:", selectedGroupId.value);
};

// 加载组内设备
const loadGroupDevices = async (groupId) => {
  try {
    const group = deviceGroups.value.find((g) => g.id === groupId);
    if (group) {
      groupDevices.value = [...group.devices];
    }
  } catch (error) {
    console.error("加载组内设备失败:", error);
  }
};

// 显示分组管理对话框
const showGroupManager = () => {
  showGroupDialog.value = true;
};

// 关闭分组对话框
const closeGroupDialog = () => {
  showGroupDialog.value = false;
};

// 保存设备组
const saveDeviceGroup = async (groupData) => {
  try {
    // 检查是否存在同名分组
    const existingGroup = deviceGroups.value.find(
      (group) => group.name === groupData.name && group.id !== groupData.id
    );

    if (existingGroup) {
      await alertService.alert({
        title: "操作失败",
        message: `已存在名为"${groupData.name}"的分组，请使用其他名称`,
        confirmButtonText: "确定",
      });
      return;
    }

    if (groupData.id) {
      // 更新组
      await electronAPI.updateDeviceGroup(groupData.id, {
        name: groupData.name,
        description: groupData.description,
      });
      console.log("更新设备组成功:", groupData.name);
    } else {
      // 创建组
      await electronAPI.createDeviceGroup(
        groupData.name,
        groupData.description
      );
      console.log("创建设备组成功:", groupData.name);
    }

    // 刷新设备组列表
    await fetchDeviceGroups();
    ElMessage.success(groupData.id ? "设备组更新成功" : "设备组创建成功");
  } catch (error) {
    console.error("保存设备组失败:", error);

    // 显示错误提示
    await alertService.alert({
      title: "操作失败",
      message: `保存设备组失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 删除设备组
const deleteDeviceGroup = async (groupId) => {
  try {
    // 调用API删除设备组
    await electronAPI.deleteDeviceGroup(groupId);
    console.log("删除设备组成功:", groupId);

    // 如果当前选中的是被删除的组，清空选择
    if (selectedGroupId.value === groupId) {
      selectedGroupId.value = null;
      groupDevices.value = [];
    }

    // 刷新设备组列表
    await fetchDeviceGroups();

    // 关闭对话框
    closeGroupDialog();
    ElMessage.success("设备组删除成功");
  } catch (error) {
    console.error("删除设备组失败:", error);

    // 显示错误提示
    await alertService.alert({
      title: "操作失败",
      message: `删除设备组失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 显示分配设备到组对话框
const showAssignToGroupDialog = () => {
  // 直接显示分配对话框
  showAssignmentDialog.value = true;
};

// 从分配对话框创建分组
const handleCreateGroupFromAssignment = () => {
  // 先隐藏分配对话框
  showAssignmentDialog.value = false;

  // 显示分组管理对话框
  showGroupManager();
};

// 处理添加设备到分组
const handleAddDeviceToGroup = async (data) => {
  try {
    await electronAPI.addDeviceToGroup(data.groupId, data.sn);
    console.log(`设备 ${data.sn} 已添加到分组`);

    // 刷新设备组列表
    await fetchDeviceGroups();
  } catch (error) {
    console.error(`添加设备到分组失败: ${error.message}`);

    // 显示错误提示
    await alertService.alert({
      title: "操作失败",
      message: `添加设备到分组失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 处理从分组中移除设备
const handleRemoveDeviceFromGroup = async (data) => {
  try {
    await electronAPI.removeDeviceFromGroup(data.groupId, data.sn);
    console.log(`设备 ${data.sn} 已从分组中移除`);

    // 刷新设备组列表
    await fetchDeviceGroups();

    // 如果当前选中的是该组，刷新组内设备
    if (selectedGroupId.value === data.groupId) {
      loadGroupDevices(data.groupId);
    }
  } catch (error) {
    console.error(`从分组中移除设备失败: ${error.message}`);

    // 显示错误提示
    await alertService.alert({
      title: "操作失败",
      message: `从分组中移除设备失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 分配设备到组
const assignDevicesToGroup = async (data) => {
  try {
    if (!data.groupId) {
      await alertService.alert({
        title: "提示",
        message: "请选择要分配到的设备组",
        confirmButtonText: "确定",
      });
      return;
    }

    // 批量添加设备到组
    let successCount = 0;
    let failCount = 0;

    for (const sn of data.devices) {
      try {
        await electronAPI.addDeviceToGroup(data.groupId, sn);
        successCount++;
      } catch (error) {
        console.error(`添加设备 ${sn} 到组失败:`, error);
        failCount++;
      }
    }

    // 刷新设备组列表
    await fetchDeviceGroups();

    // 关闭对话框
    showAssignmentDialog.value = false;

    // 显示结果
    let message = "";
    if (successCount > 0) {
      message += `成功添加 ${successCount} 个设备到组。`;
    }
    if (failCount > 0) {
      message += `${failCount} 个设备添加失败。`;
    }
    ElMessage.success(message);

    // 如果当前选中的是该组，刷新组内设备
    if (selectedGroupId.value === data.groupId) {
      loadGroupDevices(data.groupId);
    }
  } catch (error) {
    console.error("分配设备到组失败:", error);

    // 显示错误提示
    await alertService.alert({
      title: "操作失败",
      message: `分配设备到组失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 修改批量监控方法
const batchMonitor = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 检查当前监控设备数量
    const availableSlots = 6 - monitoredDevices.value.size;
    if (availableSlots <= 0) {
      ElMessage.warning(
        `当前已有 ${monitoredDevices.value.size} 台设备在监控中，最多只能监控6台设备`
      );
      return;
    }

    // 限制可监控的设备数量
    const devicesToMonitor = selectedDevices.value.slice(0, availableSlots);
    if (devicesToMonitor.length < selectedDevices.value.length) {
      ElMessage.warning(
        `由于监控数量限制，只能同时监控 ${availableSlots} 台设备，其余设备将在当前监控完成后继续`
      );
    }

    let successCount = 0;
    let failCount = 0;
    let offlineCount = 0;

    for (const sn of devicesToMonitor) {
      try {
        // 检查设备是否在线
        const isOnline = await electronAPI.isDeviceOnline(sn);
        if (!isOnline) {
          offlineCount++;
          continue;
        }

        // 创建监控命令
        const monitorCommand = {
          type: CommandType.MONITOR,
          data: { id: Date.now() },
        };

        // 发送命令
        await electronAPI.sendCommand(sn, monitorCommand);
        console.log(`设备 ${sn} 监控命令已发送`);

        // 添加到监控设备集合
        monitoredDevices.value.add(sn);

        successCount++;
      } catch (error) {
        console.error(`设备 ${sn} 监控失败:`, error);
        failCount++;
      }
    }

    // 显示结果
    let message = "未发现可监控设备";
    if (successCount > 0) {
      message = `成功发送 ${successCount} 个设备的监控命令。`;
    } else if (failCount > 0) {
      message = `${failCount} 个设备监控失败（可能离线）。`;
    }
    if (offlineCount > 0) {
      message += `${offlineCount} 个设备离线。`;
    }

    ElMessage.success(message);

    // 清空选择，退出批量模式
    selectedDevices.value = [];
    isBatchMode.value = false;
  } catch (error) {
    console.error("批量监控失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量监控失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 修改批量停止监控方法
const batchStopMonitor = async () => {
  if (monitoredDevices.value.size === 0) {
    ElMessage.success("当前没有正在监控的设备");
    return;
  }

  const confirmed = await alertService.confirm({
    title: "取消监控",
    message: `确定要取消 ${monitoredDevices.value.size} 个设备的监控吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (!confirmed) return;

  try {
    let successCount = 0;
    let failCount = 0;
    let offlineCount = 0;

    // 遍历所有监控中的设备
    for (const sn of monitoredDevices.value) {
      try {
        // 检查设备是否在线
        const device = devicesMap.value.get(sn);
        if (!device || !device.isOnline) {
          offlineCount++;
          continue;
        }

        // 创建停止监控命令
        const stopMonitorCommand = {
          type: CommandType.MONITOR_STOP,
          data: { id: Date.now() },
        };

        // 发送命令
        await electronAPI.sendCommand(sn, stopMonitorCommand);
        console.log(`设备 ${sn} 停止监控命令已发送`);
        successCount++;
      } catch (error) {
        console.error(`设备 ${sn} 停止监控失败:`, error);
        failCount++;
      }
    }

    // 显示结果
    let message = "";
    if (successCount > 0) {
      message += `成功发送 ${successCount} 个设备的停止监控命令。`;
    }
    if (failCount > 0) {
      message += `${failCount} 个设备停止监控失败。`;
    }
    if (offlineCount > 0) {
      message += `${offlineCount} 个设备离线。`;
    }

    ElMessage.success(message);

    // 清空监控设备记录
    monitoredDevices.value.clear();
  } catch (error) {
    console.error("批量停止监控失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量停止监控失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 切换批量更多菜单
const toggleBatchMoreMenu = (event) => {
  if (!showBatchMoreMenu.value) {
    // 计算菜单位置
    const buttonRect = event.currentTarget.getBoundingClientRect();
    const menuItemHeight = 40; // 每个菜单项的高度
    const menuWidth = 140; // 菜单宽度
    const menuHeight = menuItemHeight * 5; // 5个菜单项

    const windowHeight = window.innerHeight;
    const spaceBelow = windowHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    // 为向上弹出添加安全距离
    const safetyMargin = 20;

    // 如果下方空间不足，且上方空间足够，则向上弹出
    if (
      spaceBelow < menuHeight + safetyMargin &&
      spaceAbove >= menuHeight + safetyMargin
    ) {
      batchMoreMenuPosition.value = {
        top: buttonRect.bottom - menuHeight - safetyMargin + "px",
        left: buttonRect.right - menuWidth + "px",
      };
    } else {
      // 如果上方空间也不足，则强制向上弹出，但可能会被遮挡
      // 如果下方空间足够，则向下弹出
      batchMoreMenuPosition.value = {
        top: buttonRect.bottom + 5 + "px",
        left: buttonRect.right - menuWidth + "px",
      };
    }
  }

  showBatchMoreMenu.value = !showBatchMoreMenu.value;
};

// 点击外部关闭批量更多菜单
const handleClickOutside = (event) => {
  console.log("点击外部事件触发");
  console.log("当前状态:", {
    justAdjustedVolume: justAdjustedVolume.value,
    isAdjustingVolume: isAdjustingVolume.value,
    showVolumeSlider: showVolumeSlider.value,
  });

  // 如果刚刚调整过音量，忽略这次点击事件
  if (justAdjustedVolume.value) {
    console.log("刚刚调整过音量，忽略点击事件");
    justAdjustedVolume.value = false; // 重置标志
    return;
  }

  // 处理主菜单
  if (
    batchMoreMenuRef.value &&
    !batchMoreMenuRef.value.contains(event.target)
  ) {
    // 如果音量控制条正在显示，不要关闭主菜单
    if (!showVolumeSlider.value) {
      console.log("点击在主菜单外部，关闭主菜单");
      showBatchMoreMenu.value = false;
    }
  }

  // 处理音量控制
  const volumeSubmenu = document.querySelector(".volume-submenu");
  const isClickOnVolumeItem =
    volumeItemRef.value && volumeItemRef.value.contains(event.target);
  const isClickOnVolumeSubmenu =
    volumeSubmenu && volumeSubmenu.contains(event.target);

  console.log("点击位置检查:", {
    isClickOnVolumeItem,
    isClickOnVolumeSubmenu,
    isAdjustingVolume: isAdjustingVolume.value,
    volumeSubmenu: volumeSubmenu ? "存在" : "不存在",
  });

  // 如果点击在音量控制之外，并且不是正在调整音量，则隐藏
  if (
    !isClickOnVolumeItem &&
    !isClickOnVolumeSubmenu &&
    !isAdjustingVolume.value
  ) {
    console.log("点击在音量控制外部，隐藏音量控制条");
    showVolumeSlider.value = false;
  }
};

// 批量控制音量
const batchControlVolume = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 检查设备在线状态
    const onlineDevices = [];
    for (const sn of selectedDevices.value) {
      const isOnline = await electronAPI.isDeviceOnline(sn);
      if (isOnline) {
        onlineDevices.push(sn);
      }
    }

    if (onlineDevices.length === 0) {
      ElMessage.warning("没有在线设备可操作");
      return;
    }

    // 弹出音量控制对话框
    const volume = await alertService.prompt({
      title: "批量控制音量",
      message: "请输入音量值(0-100):",
      inputType: "number",
      inputValue: "50",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (volume === null) return; // 用户取消

    const volumeValue = parseInt(volume);
    if (isNaN(volumeValue) || volumeValue < 0 || volumeValue > 100) {
      await alertService.alert({
        title: "输入错误",
        message: "请输入0-100之间的数字",
        confirmButtonText: "确定",
      });
      return;
    }

    // 发送批量控制音量命令
    await electronAPI.emit("device:batch-volume", {
      deviceList: onlineDevices,
      volume: volumeValue,
    });

    showBatchMoreMenu.value = false;
  } catch (error) {
    console.error("批量控制音量失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量控制音量失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 批量关闭屏幕
const batchScreenOff = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 检查设备在线状态
    const onlineDevices = [];
    for (const sn of selectedDevices.value) {
      const isOnline = await electronAPI.isDeviceOnline(sn);
      if (isOnline) {
        onlineDevices.push(sn);
      }
    }

    if (onlineDevices.length === 0) {
      ElMessage.warning("没有在线设备可操作");
      return;
    }

    const confirmed = await alertService.confirm({
      title: "批量关闭屏幕",
      message: `确定要关闭 ${onlineDevices.length} 个设备的屏幕吗？`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) return;

    // 发送批量关闭屏幕命令
    await electronAPI.emit("device:batch-screen", {
      deviceList: onlineDevices,
      turnOn: false,
    });

    showBatchMoreMenu.value = false;
  } catch (error) {
    console.error("批量关闭屏幕失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量关闭屏幕失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 批量开启屏幕
const batchScreenOn = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 检查设备在线状态
    const onlineDevices = [];
    for (const sn of selectedDevices.value) {
      const isOnline = await electronAPI.isDeviceOnline(sn);
      if (isOnline) {
        onlineDevices.push(sn);
      }
    }

    if (onlineDevices.length === 0) {
      ElMessage.warning("没有在线设备可操作");
      return;
    }

    const confirmed = await alertService.confirm({
      title: "批量开启屏幕",
      message: `确定要开启 ${onlineDevices.length} 个设备的屏幕吗？`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) return;

    // 发送批量开启屏幕命令
    await electronAPI.emit("device:batch-screen", {
      deviceList: onlineDevices,
      turnOn: true,
    });

    showBatchMoreMenu.value = false;
  } catch (error) {
    console.error("批量开启屏幕失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量开启屏幕失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 批量隐藏快捷入口
const batchHideShortcut = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 检查设备在线状态
    const onlineDevices = [];
    for (const sn of selectedDevices.value) {
      const isOnline = await electronAPI.isDeviceOnline(sn);
      if (isOnline) {
        onlineDevices.push(sn);
      }
    }

    if (onlineDevices.length === 0) {
      ElMessage.warning("没有在线设备可操作");
      return;
    }

    const confirmed = await alertService.confirm({
      title: "批量隐藏快捷入口",
      message: `确定要隐藏 ${onlineDevices.length} 个设备的快捷入口吗？`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) return;

    // 发送批量隐藏快捷入口命令
    await electronAPI.emit("device:batch-shortcut", {
      deviceList: onlineDevices,
      show: false,
    });

    showBatchMoreMenu.value = false;
  } catch (error) {
    console.error("批量隐藏快捷入口失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量隐藏快捷入口失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 批量显示快捷入口
const batchShowShortcut = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 检查设备在线状态
    const onlineDevices = [];
    for (const sn of selectedDevices.value) {
      const isOnline = await electronAPI.isDeviceOnline(sn);
      if (isOnline) {
        onlineDevices.push(sn);
      }
    }

    if (onlineDevices.length === 0) {
      ElMessage.warning("没有在线设备可操作");
      return;
    }

    const confirmed = await alertService.confirm({
      title: "批量显示快捷入口",
      message: `确定要显示 ${onlineDevices.length} 个设备的快捷入口吗？`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) return;

    // 发送批量显示快捷入口命令
    await electronAPI.emit("device:batch-shortcut", {
      deviceList: onlineDevices,
      show: true,
    });

    showBatchMoreMenu.value = false;
  } catch (error) {
    console.error("批量显示快捷入口失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: `批量显示快捷入口失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 处理音量变化
const handleVolumeChange = (event) => {
  volumeValue.value = event.target.value;
};

// 在 setup 中添加新的状态变量
const initialVolumeValue = ref(50);

// 添加v-click-outside指令
const vClickOutside = {
  mounted(el, binding) {
    el._clickOutside = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event);
      }
    };
    document.addEventListener("click", el._clickOutside);
  },
  unmounted(el) {
    document.removeEventListener("click", el._clickOutside);
  },
};

// 注册指令
const app = getCurrentInstance();
if (app) {
  app.appContext.app.directive("click-outside", vClickOutside);
}

// 批量重置设备
const batchReset = async () => {
  if (selectedDevices.value.length === 0) return;

  const confirmed = await alertService.confirm({
    title: "批量重置设备",
    message: `确定要重置选中的 ${selectedDevices.value.length} 个设备吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      let successCount = 0;
      let failCount = 0;

      for (const sn of selectedDevices.value) {
        try {
          // 检查设备是否在线
          const isOnline = await electronAPI.isDeviceOnline(sn);
          if (!isOnline) {
            failCount++;
            continue;
          }

          // 发送重置命令
          const resetCommand = {
            type: CommandType.RESET,
            data: { id: Date.now() },
          };
          await electronAPI.sendCommand(sn, resetCommand);
          console.log(`设备 ${sn} 重置命令已发送`);

          // 手动更新设备状态为离线
          const deviceInMap = devicesMap.value.get(sn);
          if (deviceInMap) {
            const updatedDevice = {
              ...deviceInMap,
              isOnline: false,
              deviceStatus: 0,
              battery: "-",
              onuse: false,
              playStatus: "-",
              controlled: false,
            };

            // 更新设备Map
            devicesMap.value.set(sn, updatedDevice);
          }

          successCount++;
        } catch (error) {
          console.error(`重置设备 ${sn} 失败:`, error);
          failCount++;
        }
      }

      // 更新设备数组
      mapToListAndRank();

      // 显示结果
      let message = "";
      if (successCount > 0) {
        message += `成功发送 ${successCount} 个设备的重置命令。`;
      }
      if (failCount > 0) {
        message += `${failCount} 个设备重置失败（可能离线）。`;
      }

      ElMessage.success(message);

      // 清空选择
      selectedDevices.value = [];
    } catch (error) {
      console.error("批量重置设备失败:", error);
      await alertService.alert({
        title: "操作失败",
        message: `批量重置设备失败: ${error.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};

// 批量重置视野
const batchResetView = async () => {
  if (selectedDevices.value.length === 0) return;

  const confirmed = await alertService.confirm({
    title: "批量重置视野",
    message: `确定要重置选中的 ${selectedDevices.value.length} 个设备的视野吗？`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (confirmed) {
    try {
      let successCount = 0;
      let failCount = 0;
      let offlineCount = 0;

      for (const sn of selectedDevices.value) {
        try {
          // 检查设备是否在线
          const isOnline = await electronAPI.isDeviceOnline(sn);
          if (!isOnline) {
            offlineCount++;
            continue;
          }

          // 发送重置视野命令
          const resetViewCommand = {
            type: CommandType.RESET_VIEW,
            data: { id: Date.now() },
          };
          await electronAPI.sendCommand(sn, resetViewCommand);
          console.log(`设备 ${sn} 重置视野命令已发送`);

          successCount++;
        } catch (error) {
          console.error(`重置设备 ${sn} 视野失败:`, error);
          failCount++;
        }
      }

      // 显示结果
      let message = "";
      if (successCount > 0) {
        message += `成功发送 ${successCount} 个设备的重置视野命令。`;
      }
      if (failCount > 0) {
        message += `${failCount} 个设备重置视野失败。`;
      }
      if (offlineCount > 0) {
        message += `${offlineCount} 个设备离线。`;
      }

      ElMessage.success(message);

      // 清空选择
      selectedDevices.value = [];
    } catch (error) {
      console.error("批量重置视野失败:", error);
      await alertService.alert({
        title: "操作失败",
        message: `批量重置视野失败: ${error.message || "未知错误"}`,
        confirmButtonText: "确定",
      });
    }
  }
};
</script>

<style scoped>
.page {
  padding: 0;
}
/* 头部 */
.device-status-bar {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  font-size: 14px;
  flex-wrap: wrap;
  padding: 26px 30px;
}

.device-count {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 16px;
  color: var(--color-menu-text);
  text-align: center;
  font-size: 18px;
  font-style: normal;

  font-weight: 600;
  line-height: 18px; /* 100% */
  .online {
    color: #407cf7;
  }
  .slash {
    padding: 0;
    margin: 0;
  }
}

.status-groups {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
}

.status-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  .status-label {
    color: #989898;
    font-size: 12px;
    font-style: normal;
    font-weight: 340;
    line-height: normal;
  }
}

.status-icon {
  width: 16px;
  height: 16px;
}

.divider {
  width: 1px;
  height: 19px;
  background: #7f7f7f;
  margin: 0 12px;
}

.header {
  padding: 10px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.header-actions {
  display: flex;
  gap: 24px;
}
.text-btn {
  color: var(--color-menu-text);
  font-size: 20px;
  cursor: pointer;
  font-style: normal;
  font-weight: 340;
  line-height: normal;

  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
}
.text-btn:hover,
.text-btn.active {
  background-color: var(--color-card-background-online);
}

.content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  margin: var(--spacing-xs);
  width: calc(100% - var(--spacing-xs) * 2);
}

.device-content {
  flex: 1;
  padding: 0 30px;
  overflow: auto;
}

/* 批量操作工具栏 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.batch-toolbar.hidden {
  transform: translateY(-100%);
  opacity: 0;
  pointer-events: none;
}

.batch-selection {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 20px;
  min-width: 80px;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.select-all-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-custom {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: var(--color-card-background);
  border: 2px solid var(--color-border);
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.2s;
}

.select-all-checkbox input[type="checkbox"]:checked ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.select-all-checkbox input[type="checkbox"]:checked ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 4px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.select-all-checkbox input[type="checkbox"]:indeterminate ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.selected-count {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.batch-actions {
  display: flex;
  gap: 12px;
}

.icon-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  background-color: var(--color-dialog-background);
  border-radius: var(--border-radius-sm);
  padding: 8px 12px;

  color: var(--color-menu-text);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.icon-btn.disabled:hover {
  cursor: not-allowed;
  transform: scale(0.95); /* 悬停时轻微缩小，增强反馈 */
  transition: all 0.2s; /* 添加过渡动画 */
}
.icon-btn img {
  width: 20px;
}
.more-btn {
  padding: 0 12px 0 2px;
}
.more-btn img {
  width: 28px;
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--color-danger-dark, #d32f2f);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border-light);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误状态 */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: var(--color-danger);
}

.error p {
  margin-bottom: var(--spacing-md);
}

/* 空列表状态 */
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  text-align: center;
}
.empty-list img {
  cursor: pointer;
}
.empty-list span {
  margin: 38px 0 18px;
}

.empty-icon {
  font-size: 48px;
  color: var(--color-text-disabled);
  margin-bottom: var(--spacing-md);
}

.empty-list h2 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--color-text-primary);
}

.empty-list p {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--color-text-secondary);
}

/* 设备列表 */
.device-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.device-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-filter,
.custom-select {
  background-color: var(--color-card-background);
  color: var(--color-text-primary);
  border: none;
}
.custom-select {
  min-width: 120px;
}

.device-list-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.btn-text {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: var(--color-text-secondary);
}

.btn-text.active {
  color: var(--color-primary);
}

/* 设备网格 */
.device-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 每行固定3列 */
  gap: 20px;
  margin-top: var(--spacing-sm);
  grid-auto-rows: min-content;
  justify-content: left; /* 如果你希望左对齐可以保留，如果想居中请改成 center */
}

.device-grid.list-view {
  grid-template-columns: minmax(300px, 700px);
  justify-content: center; /* 居中显示列表 */
}

/* 设备卡片样式已移至DeviceCard.vue组件 */

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius-sm);
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn i {
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark, #0c7cd5);
}

.btn-secondary {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark, #e6e6e6);
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-success:hover {
  background-color: var(--color-success-dark, #449d44);
}

.btn-disabled {
  background-color: var(--color-background);
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

.btn-sm {
  padding: 4px 12px;
  font-size: 12px;
}

/* 图标样式已移至全局样式文件 */
.add-tips {
  text-align: left;
}

.batch-more-menu {
  position: fixed;
  background: var(--color-card-background-online);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  width: 140px;
  z-index: 1000;
  animation: fadeIn 0.15s ease-out;
  border: 1px solid var(--color-border);
}

/* 深色主题下的批量更多菜单 */
[data-theme="dark"] .batch-more-menu {
  background-color: #2d3748;
}

.dropdown-item {
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-text-primary);
  border-radius: 4px;
  margin: 2px 4px;
}

.dropdown-item:hover {
  background-color: var(--color-background-light);
  transform: translateX(2px);
}

/* 深色主题下的菜单项 */
[data-theme="dark"] .dropdown-item {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 音量控制子菜单 */
.volume-submenu {
  position: fixed;
  background-color: var(--color-white);
  border-radius: 6px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  padding: 12px;
  width: 60px;
  height: 180px;
  z-index: 1001;
  animation: fadeIn 0.15s ease-out;
}

/* 深色主题下的音量控制子菜单 */
[data-theme="dark"] .volume-submenu {
  background-color: #2d3748;
}

.volume-slider-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.volume-value {
  font-size: 12px;
  color: var(--color-text);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-slider {
  width: 6px;
  height: 120px;
  -webkit-appearance: none;
  appearance: none;
  background: var(--color-border);
  outline: none;
  border-radius: 3px;
  cursor: pointer;
  writing-mode: bt-lr;
  -webkit-appearance: slider-vertical;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.loading-indicator-small {
  width: 12px;
  height: 12px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
:deep(.custom-select .el-select__wrapper) {
  background-color: #eff6fe;
  box-shadow: none;
  color: var(--color-menu-text);
}
:deep(.custom-select .el-select__placeholder.is-transparent) {
  color: var(--color-menu-text);
}
</style>
